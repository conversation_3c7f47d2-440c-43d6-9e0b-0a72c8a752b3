// 欢迎场景
import BaseScene from './BaseScene'

export default class WelcomeScene extends BaseScene {
  constructor(game) {
    super(game)
    
    // 添加规则说明状态
    this.showRules = false
    
    // 添加困难模式状态
    if (wx.ad._shen_he()) {
      this.hardMode = false
    } else {
      this.hardMode = true
    }
    
    // 检测屏幕方向
    this.isLandscape = true
    
    // 根据屏幕方向选择布局配置
    this.layoutConfig = this.isLandscape 
      ? this.config.layout.landscape 
      : this.config.layout.portrait
    
    // 计算复选框位置和大小
    this.calculateCheckboxLayout()

    // 加载背景图片
    this.bgImage = wx.createImage()
    this.bgImage.src = 'images/bg.png'
  }
  
  init() {
    // 初始化触摸事件
    this.bindTouchEvents()
    wx.ad.showBanner()
    wx.ad.showCha()
  }
  
  bindTouchEvents() {
    this.touchHandler = this.onTouchStart.bind(this)
    wx.onTouchStart(this.touchHandler)
  }
  
  onTouchStart(e) {
    const touch = e.touches[0]
    const x = touch.clientX
    const y = touch.clientY
    
    // 检测复选框点击
    if (x >= this.checkboxX && x <= this.checkboxX + this.checkboxSize &&
        y >= this.checkboxY && y <= this.checkboxY + this.checkboxSize) {
      this.hardMode = !this.hardMode
      return
    }
    
    // 检测游戏规则按钮点击
    const ruleBtnWidth = 120
    const ruleBtnHeight = 40
    const ruleBtnX = 20
    const ruleBtnY = 20
    
    if (x >= ruleBtnX && x <= ruleBtnX + ruleBtnWidth && 
        y >= ruleBtnY && y <= ruleBtnY + ruleBtnHeight) {
      console.log('游戏规则按钮被点击')
      this.showRules = !this.showRules
      return
    }
    
    // 计算按钮位置（使用布局配置）
    const startBtnWidth = 200
    const startBtnHeight = 60
    const startBtnX = this.width / 2 - startBtnWidth / 2
    const startBtnY = this.height * this.layoutConfig.startBtnY
    
    // 检测是否点击了开始按钮
    if (x >= startBtnX && x <= startBtnX + startBtnWidth && 
        y >= startBtnY && y <= startBtnY + startBtnHeight) {
      console.log('开始游戏按钮被点击')
      // 直接开始第一关
      this.game.dataManager.setCurrentLevel(0)
      this.startGame()
    }
    
    // 计算关卡选择按钮位置
    const levelBtnWidth = 200
    const levelBtnHeight = 60
    const levelBtnX = this.width / 2 - levelBtnWidth / 2
    const levelBtnY = this.height * this.layoutConfig.levelBtnY
    
    // 检测是否点击了关卡选择按钮
    if (x >= levelBtnX && x <= levelBtnX + levelBtnWidth && 
        y >= levelBtnY && y <= levelBtnY + levelBtnHeight) {
      console.log('关卡选择按钮被点击')
      this.game.sceneManager.showScene('levelSelect')
    }
    
    // 如果规则面板打开，点击任意位置关闭
    if (this.showRules) {
      this.showRules = false
      return
    }
  }
  
  render() {
    super.render()
    
    // 绘制背景图片
    if (this.bgImage) {
      // 计算缩放比例以覆盖整个画布
      const scale = Math.max(
        this.width / this.bgImage.width,
        this.height / this.bgImage.height
      )
      
      // 计算绘制尺寸
      const drawWidth = this.bgImage.width * scale
      const drawHeight = this.bgImage.height * scale
      
      // 计算居中位置
      const x = (this.width - drawWidth) / 2
      const y = (this.height - drawHeight) / 2
      
      // 绘制背景图片
      this.ctx.drawImage(
        this.bgImage,
        x, y,
        drawWidth, drawHeight
      )
    }

    // 添加半透明遮罩使文字更清晰
    // this.ctx.fillStyle = 'rgba(255, 255, 255, 0.4)'
    // this.ctx.fillRect(0, 0, this.width, this.height)
    
    // 使用布局配置绘制标题
    // 先绘制标题阴影
    // this.drawText(
    //   '我是猫',
    //   this.width / 2 + 2,
    //   this.height * this.layoutConfig.titleY + 2,
    //   this.config.fonts.title,
    //   'rgba(0, 0, 0, 0.1)',
    //   'center'
    // )
    
    // 绘制主标题
    // this.drawText(
    //   '我是猫',
    //   this.width / 2,
    //   this.height * this.layoutConfig.titleY,
    //   this.config.fonts.title,
    //   '#B768A2',  // 改为紫色
    //   'center'
    // )
    
    // 绘制副标题
    // this.drawText(
    //   '关于猫咪的知识你知道多少？', 
    //   this.width / 2, 
    //   this.height * this.layoutConfig.subtitleY, 
    //   this.config.fonts.body, 
    //   '#ffffff'  // 使用深灰色
    // )
    
    // 如果没有显示规则面板，才绘制按钮和其他UI元素
    if (!this.showRules) {
      // 绘制开始按钮
      const startBtnWidth = 200
      const startBtnHeight = 60
      const startBtnX = this.width / 2 - startBtnWidth / 2
      const startBtnY = this.height * this.layoutConfig.startBtnY
      
      // 绘制按钮阴影
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'
      this.drawRoundRect(startBtnX + 2, startBtnY + 2, startBtnWidth, startBtnHeight, 30, 'rgba(0, 0, 0, 0.1)')
      
      // 绘制按钮背景
      this.drawRoundRect(startBtnX, startBtnY, startBtnWidth, startBtnHeight, 30, this.config.colors.primary)
      
      if (this.hardMode) {
        // 计算图标位置（在文字右侧）
        const iconSize = 24
        const iconX = startBtnX + startBtnWidth - iconSize - 15 // 距离右边缘15像素
        const iconY = startBtnY + (startBtnHeight - iconSize) / 2 // 垂直居中
        
        // 绘制播放图标背景圆圈
        this.ctx.beginPath()
        this.ctx.arc(iconX + iconSize/2, iconY + iconSize/2, iconSize/2, 0, Math.PI * 2)
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)'
        this.ctx.fill()
        
        // 绘制播放三角形
        this.ctx.beginPath()
        this.ctx.moveTo(iconX + iconSize/3, iconY + iconSize/4)
        this.ctx.lineTo(iconX + iconSize/3, iconY + iconSize * 3/4)
        this.ctx.lineTo(iconX + iconSize * 3/4, iconY + iconSize/2)
        this.ctx.closePath()
        this.ctx.fillStyle = '#ffffff'
        this.ctx.fill()
        
        // 绘制文字（向左偏移以给图标留出空间）
        this.ctx.fillStyle = '#ffffff'
        this.ctx.font = this.config.fonts.subtitle
        this.ctx.textAlign = 'center'
        this.ctx.textBaseline = 'middle'
        this.ctx.fillText(
          '开始游戏',
          startBtnX + startBtnWidth/2 - iconSize/2,
          startBtnY + startBtnHeight/2
        )
      } else {
        // 普通模式下居中绘制文字
        this.ctx.fillStyle = '#ffffff'
        this.ctx.font = this.config.fonts.subtitle
        this.ctx.textAlign = 'center'
        this.ctx.textBaseline = 'middle'
        this.ctx.fillText(
          '开始游戏',
          startBtnX + startBtnWidth/2,
          startBtnY + startBtnHeight/2
        )
      }
      
      // 绘制关卡选择按钮
      const levelBtnWidth = 200
      const levelBtnHeight = 60
      const levelBtnX = this.width / 2 - levelBtnWidth / 2
      const levelBtnY = this.height * this.layoutConfig.levelBtnY
      
      // 绘制按钮阴影
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'
      this.drawRoundRect(levelBtnX + 2, levelBtnY + 2, levelBtnWidth, levelBtnHeight, 30, 'rgba(0, 0, 0, 0.1)')
      
      // 绘制按钮背景
      this.drawRoundRect(levelBtnX, levelBtnY, levelBtnWidth, levelBtnHeight, 30, '#7C8B95')
      
      // 绘制文字
      this.ctx.fillStyle = '#ffffff'
      this.ctx.font = this.config.fonts.subtitle
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText(
        '选择关卡',
        levelBtnX + levelBtnWidth/2,
        levelBtnY + levelBtnHeight/2
      )
      
      // 绘制复选框
      if (!wx.ad._shen_he()) {
          this.ctx.strokeStyle = '#8BA888'
          this.ctx.lineWidth = 2
          this.ctx.strokeRect(this.checkboxX, this.checkboxY, this.checkboxSize, this.checkboxSize)
      }
      
      // 如果选中，绘制勾选标记
      if (this.hardMode) {
        this.ctx.beginPath()
        this.ctx.moveTo(this.checkboxX + 5, this.checkboxY + 12)
        this.ctx.lineTo(this.checkboxX + 10, this.checkboxY + 17)
        this.ctx.lineTo(this.checkboxX + 19, this.checkboxY + 7)
        this.ctx.strokeStyle = '#8BA888'
        this.ctx.lineWidth = 3
        this.ctx.stroke()
      }
      
      // 绘制文本标签
      if (!wx.ad._shen_he()) {
          this.drawText(
            '困难模式',
            this.checkboxX + this.checkboxSize + this.checkboxGap,
            this.checkboxY + this.checkboxSize/2,
            this.config.fonts.body,
            '#FFFFFF',
            'left'
          )
      }
      
      // 绘制版本信息
      this.drawText(
        '版本: 1.1.0', 
        this.width / 2, 
        this.height - 20, 
        this.config.fonts.small, 
        '#999999'
      )

      // 绘制游戏规则按钮
      const ruleBtnWidth = 120
      const ruleBtnHeight = 40
      const ruleBtnX = 20
      const ruleBtnY = 20
      
      // 绘制按钮阴影 - 使用较小的圆角值(8)
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'
      this.drawRoundRect(ruleBtnX + 2, ruleBtnY + 2, ruleBtnWidth, ruleBtnHeight, 15, 'rgba(0, 0, 0, 0.1)')
      
      // 绘制按钮背景 - 使用较小的圆角值(8)
      this.drawRoundRect(ruleBtnX, ruleBtnY, ruleBtnWidth, ruleBtnHeight, 15, '#424242')
      
      // 绘制按钮文字
      this.ctx.fillStyle = '#ffffff'
      this.ctx.font = this.config.fonts.body
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText(
        '冒险指南',
        ruleBtnX + ruleBtnWidth/2,
        ruleBtnY + ruleBtnHeight/2
      )
    }
    
    // 如果显示规则，在最后绘制规则面板，确保它在最上层
    if (this.showRules) {
      // 绘制半透明背景
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
      this.ctx.fillRect(0, 0, this.width, this.height)
      
      // 绘制规则面板 - 横屏时调整宽高比
      const panelWidth = this.isLandscape ? this.width * 0.6 : this.width * 0.85
      const panelHeight = this.isLandscape ? this.height * 0.8 : this.height * 0.7
      const panelX = this.width / 2 - panelWidth / 2
      const panelY = this.height / 2 - panelHeight / 2
      
      // 绘制面板阴影
      this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)'
      this.ctx.shadowBlur = 15
      this.ctx.shadowOffsetY = 5
      this.drawRoundRect(panelX, panelY, panelWidth, panelHeight, 15, '#FFFFFF')
      this.ctx.shadowColor = 'transparent'
      
      // 绘制标题
      this.drawText(
        '冒险指南',
        this.width / 2,
        panelY + 40,
        this.config.fonts.subtitle,
        this.config.colors.dark,
        'center'
      )
      
      // 绘制规则内容
      const rules = [
        '1. 你将探索一片充满诡异生物的神秘森林',
        '2. 收集七种神秘的诡异生物是你的主要任务',
        '3. 回答正确的问题来通过各个区域的挑战',
        '4. 普通模式每题20秒思考时间',
        '5. 困难模式时间减半，面对更强大的生物',
        '6. 注意观察环境，解开隐藏的谜题',
        '7. 收集足够分数解锁新区域，揭开森林秘密'
      ]
      
      // 横屏模式下调整规则文本布局
      let y = panelY + 90
      const lineHeight = this.isLandscape ? 30 : 35
      
      for (const rule of rules) {
        this.drawText(
          rule,
          panelX + 30,
          y,
          this.config.fonts.body,
          '#555555',
          'left'
        )
        y += lineHeight
      }
    }
  }
  
  destroy() {
    // 清理事件
    wx.offTouchStart(this.touchHandler)
    wx.ad.hideBanner()
  }
  
  // 添加复选框布局计算方法
  calculateCheckboxLayout() {
    // 复选框尺寸
    this.checkboxSize = 24
    this.checkboxGap = 10
    
    if (this.isLandscape) {
      // 横屏模式下，复选框在开始按钮右侧
      const startBtnWidth = 200
      const startBtnX = this.width / 2 - startBtnWidth / 2
      
      this.checkboxX = startBtnX + startBtnWidth + 30
      this.checkboxY = this.height * this.layoutConfig.startBtnY + 18 // 垂直居中对齐
    } else {
      // 竖屏模式下，复选框在开始按钮下方
      this.checkboxX = this.width / 2 - 60
      this.checkboxY = this.height * this.layoutConfig.startBtnY + 80
    }
  }
  
  // 修改开始游戏方法，添加广告逻辑
  startGame() {
    if (this.hardMode) {
      // 如果是困难模式，先播放广告
      console.log('播放广告...')
      wx.ad.showVideo(() => {
        // 广告播放完成后再开始游戏
        this.game.config.hardMode = this.hardMode
        this.game.startGame()
      })
    } else {
      // 普通模式直接开始游戏
      this.game.config.hardMode = this.hardMode
      this.game.startGame()
    }
  }
} 