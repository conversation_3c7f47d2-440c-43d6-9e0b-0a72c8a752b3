export default class DataManager {
  constructor() {
    // 游戏关卡配置
    this.levels = [
      {
        id: 1,
        name: "森林入口",
        description: "阴森恐怖的森林入口，第一次与诡异生物相遇",
        passingScore: 3,
        timePerQuestion: 20,
        unlocked: true,
        completed: false,
        stars: 0,
        questions: [
          {
            question: '在森林探索时，遇到耐克鲨鱼应该采取什么行动？',
            options: ['直接逃跑', '躲在树后观察', '尝试接近它', '朝它大声喊叫'],
            correctIndex: 1
          },
          {
            question: '在森林中行走时，最安全的方式是？',
            options: ['沿着小径行走', '走在树木间的缝隙', '走在开阔地带', '踩着落叶前进'],
            correctIndex: 0
          },
          {
            question: '手电筒的最佳使用时机是？',
            options: ['一直开着照明', '只在极度黑暗时使用', '交替开关以节约电量', '只在发现生物时照射'],
            correctIndex: 1
          },
          {
            question: '当听到森林中奇怪声响时，应该？',
            options: ['立即调查声音来源', '原地不动仔细聆听', '朝反方向快速移动', '发出声音吓退可能的威胁'],
            correctIndex: 2
          }
        ]
      },
      {
        id: 2,
        name: "诡异遗迹",
        description: "森林深处的古老遗迹，蕴含着解谜线索和未知危险",
        passingScore: 4,
        timePerQuestion: 20,
        unlocked: false,
        completed: false,
        stars: 0,
        questions: [
          {
            question: '面对墙壁上的神秘符号，正确的做法是？',
            options: ['忽略它们继续前进', '用手触摸符号', '仔细记录符号图案', '用工具刮擦符号'],
            correctIndex: 2
          },
          {
            question: '解开镜子谜题的关键是？',
            options: ['打碎所有镜子', '调整镜子角度反射光线', '在镜子前做特定动作', '用水清洗镜面'],
            correctIndex: 1
          },
          {
            question: '遇到木棍人通通通时，最有效的应对方式是？',
            options: ['快速绕圈跑动', '保持静止不动', '尝试模仿它的动作', '利用地形阻挡它的视线'],
            correctIndex: 3
          },
          {
            question: '在古老祭坛前，应该？',
            options: ['将收集的物品放在祭坛上', '避开祭坛范围', '围绕祭坛走三圈', '对祭坛进行考古记录'],
            correctIndex: 0
          },
          {
            question: '发现隐藏机关的最佳方法是？',
            options: ['随机按压墙壁', '寻找与众不同的纹理或图案', '使用特殊道具探测', '听声辨位'],
            correctIndex: 1
          }
        ]
      },
      {
        id: 3,
        name: "生物收集",
        description: "寻找并收集七种神秘的诡异生物",
        passingScore: 4,
        timePerQuestion: 20,
        unlocked: false,
        completed: false,
        stars: 0,
        questions: [
          {
            question: '仙人掌大象使用时间系技能时，正确的应对是？',
            options: ['快速移动躲避', '保持不规则移动', '在时停前找掩体', '使用闪光道具干扰'],
            correctIndex: 2
          },
          {
            question: '收集诡异生物的最佳工具是？',
            options: ['特制捕捉网', '照相机', '诱饵陷阱', '声音录制器'],
            correctIndex: 0
          },
          {
            question: '当多种生物同时出现时，应优先处理？',
            options: ['体型最大的生物', '速度最快的生物', '最靠近你的生物', '具有远程攻击能力的生物'],
            correctIndex: 3
          },
          {
            question: '识别隐形生物的方法是观察？',
            options: ['地面上的足迹', '空气中的波动', '周围环境的异常反应', '温度的变化'],
            correctIndex: 2
          },
          {
            question: '成功收集全部七种生物后，应该前往？',
            options: ['森林出口', '中央祭坛', '最初的入口', '最高的树塔'],
            correctIndex: 1
          }
        ]
      },
      {
        id: 4,
        name: "最终谜题",
        description: "解开森林的终极秘密，完成探索",
        passingScore: 5,
        timePerQuestion: 25,
        unlocked: false,
        completed: false,
        stars: 0,
        questions: [
          {
            question: '面对最终BOSS，最有效的战术是？',
            options: ['正面强攻', '分析其行动模式', '利用环境进行战斗', '寻找其弱点'],
            correctIndex: 3
          },
          {
            question: '解开森林最终谜题需要？',
            options: ['所有收集的生物', '特定顺序的操作', '解读古老文字', '以上全部'],
            correctIndex: 3
          },
          {
            question: '遭遇恐怖meme跳脸时，最冷静的反应是？',
            options: ['闭上眼睛', '保持镇定继续前进', '使用特殊道具防御', '快速转身后退'],
            correctIndex: 1
          },
          {
            question: '成功逃离森林的关键是？',
            options: ['找到隐藏的地图', '完成所有收集任务', '解开所有谜题', '跟随特定的标记'],
            correctIndex: 2
          },
          {
            question: '在游戏的最后阶段，哪种资源最为重要？',
            options: ['生命值', '移动速度', '道具数量', '收集的线索'],
            correctIndex: 3
          }
        ]
      }
    ];
    
    // 添加当前关卡索引
    this.currentLevelIndex = 0;
    
    // 初始化游戏状态
    this.resetGame();
    
    // 确保第一关始终解锁
    if (this.levels.length > 0) {
      this.levels[0].unlocked = true;
    }
    
    // 从本地存储加载游戏进度
    this.loadProgress();
  }
  
  // 重置当前游戏状态
  resetGame() {
    this.gameState = {
      score: 0,
      currentQuestionIndex: 0,
      selectedOption: null,
      answered: false,
      stars: 0
    };
  }
  
  // 获取当前关卡
  getCurrentLevel() {
    return this.levels[this.currentLevelIndex];
  }
  
  // 设置当前关卡
  setCurrentLevel(index) {
    if (index >= 0 && index < this.levels.length) {
      this.currentLevelIndex = index;
      this.resetGame();
      return true;
    }
    return false;
  }
  
  // 获取所有关卡
  getAllLevels() {
    return this.levels;
  }
  
  // 解锁下一关卡
  unlockNextLevel() {
    const nextLevelIndex = this.currentLevelIndex + 1;
    if (nextLevelIndex < this.levels.length) {
      // 只有当下一关未解锁时才更新状态
      if (!this.levels[nextLevelIndex].unlocked) {
        console.log(`解锁下一关卡: ${this.levels[nextLevelIndex].name}`);
        this.levels[nextLevelIndex].unlocked = true;
        this.saveProgress();
        
        // 返回新解锁的关卡信息，用于显示提示
        return {
          newlyUnlocked: true,
          level: this.levels[nextLevelIndex]
        };
      } else {
        return {
          newlyUnlocked: false
        };
      }
    }
    return {
      newlyUnlocked: false
    };
  }
  
  // 保存游戏进度到本地存储
  saveProgress() {
    try {
      const progress = {
        levels: this.levels.map(level => ({
          id: level.id,
          unlocked: level.unlocked,
          completed: level.completed,
          stars: level.stars
        }))
      };
      wx.setStorageSync('gameProgress', JSON.stringify(progress));
      console.log('游戏进度已保存');
    } catch (e) {
      console.error('保存游戏进度失败:', e);
    }
  }
  
  // 从本地存储加载游戏进度
  loadProgress() {
    try {
      const progressData = wx.getStorageSync('gameProgress');
      if (progressData) {
        const progress = JSON.parse(progressData);
        
        // 更新关卡状态
        if (progress.levels) {
          progress.levels.forEach(savedLevel => {
            const level = this.levels.find(l => l.id === savedLevel.id);
            if (level) {
              level.unlocked = savedLevel.unlocked;
              level.completed = savedLevel.completed;
              level.stars = savedLevel.stars;
            }
          });
        }
        
        console.log('游戏进度已加载');
      }
    } catch (e) {
      console.error('加载游戏进度失败:', e);
    }
  }
  
  // 更新当前关卡的星级
  updateLevelStars() {
    const level = this.getCurrentLevel();
    const totalQuestions = level.questions.length;
    const scorePercentage = (this.gameState.score / totalQuestions) * 100;
    
    let stars = 0;
    if (scorePercentage >= 90) {
      stars = 3;
    } else if (scorePercentage >= 70) {
      stars = 2;
    } else if (scorePercentage >= 50) {
      stars = 1;
    }
    
    // 只有新的星级更高时才更新
    if (stars > level.stars) {
      level.stars = stars;
      level.completed = true;
      this.saveProgress();
    }
    
    return stars;
  }
  
  // 获取当前问题
  getCurrentQuestion() {
    const level = this.getCurrentLevel();
    return level.questions[this.gameState.currentQuestionIndex];
  }
  
  // 选择选项
  selectOption(index) {
    if (this.gameState.answered) return false;
    
    this.gameState.selectedOption = index;
    this.gameState.answered = true;
    
    const currentQuestion = this.getCurrentQuestion();
    const isCorrect = index === currentQuestion.correctIndex;
    
    if (isCorrect) {
      this.gameState.score++;
    }
    
    return isCorrect;
  }
  
  // 是否有下一题
  hasNextQuestion() {
    const level = this.getCurrentLevel();
    return this.gameState.currentQuestionIndex < level.questions.length - 1;
  }
  
  // 进入下一题
  nextQuestion() {
    if (!this.hasNextQuestion()) return false;
    
    this.gameState.currentQuestionIndex++;
    this.gameState.selectedOption = null;
    this.gameState.answered = false;
    
    return true;
  }
  
  // 获取总题目数
  getTotalQuestions() {
    const level = this.getCurrentLevel();
    return level.questions.length;
  }
  
  // 判断游戏是否通关
  isGamePassed(passingScore) {
    return this.gameState.score >= passingScore;
  }
} 