import BaseScene from './BaseScene'
import AnimationManager from '../utils/AnimationManager'

export default class LevelSelectScene extends BaseScene {
  constructor(game) {
    super(game)
    this.dataManager = game.dataManager
    this.levels = this.dataManager.getAllLevels()
    
    // 滚动相关
    this.scrollX = 0
    this.maxScrollX = 0
    this.isDragging = false
    this.startX = 0
    this.lastX = 0
    
    // 初始化动画管理器
    this.animationManager = new AnimationManager()

    // 加载背景图片
    this.bgImage = wx.createImage()
    this.bgImage.src = 'images/bg2.png'
  }
  
  init() {
    // 计算最大滚动距离
    const totalWidth = this.levels.length * 250 + 100 // 每个关卡宽度 + 右侧间距
    this.maxScrollX = Math.max(0, totalWidth - this.width)
    
    // 初始化触摸事件
    this.bindTouchEvents()
  }
  
  bindTouchEvents() {
    this.touchStartHandler = this.onTouchStart.bind(this)
    this.touchMoveHandler = this.onTouchMove.bind(this)
    this.touchEndHandler = this.onTouchEnd.bind(this)
    
    wx.onTouchStart(this.touchStartHandler)
    wx.onTouchMove(this.touchMoveHandler)
    wx.onTouchEnd(this.touchEndHandler)
  }
  
  onTouchStart(e) {
    const touch = e.touches[0]
    this.isDragging = true
    this.startX = touch.clientX
    this.lastX = touch.clientX
  }
  
  onTouchMove(e) {
    if (!this.isDragging) return
    
    const touch = e.touches[0]
    const deltaX = touch.clientX - this.lastX
    this.scrollX = Math.max(0, Math.min(this.maxScrollX, this.scrollX - deltaX))
    this.lastX = touch.clientX
  }
  
  onTouchEnd(e) {
    if (!this.isDragging) return
    
    this.isDragging = false
    
    // 检查是否是点击而不是滚动
    const touch = e.changedTouches[0]
    const deltaX = Math.abs(touch.clientX - this.startX)
    
    if (deltaX < 10) {
      // 这是一个点击，检查是否点击了关卡
      this.checkLevelClick(touch.clientX + this.scrollX, touch.clientY)
    }
  }
  
  checkLevelClick(x, y) {
    const levelWidth = 200
    const levelHeight = 250
    const startY = this.height / 2 - levelHeight / 2
    
    for (let i = 0; i < this.levels.length; i++) {
      const level = this.levels[i]
      const levelX = 100 + i * 250 - this.scrollX
      
      if (x >= levelX && x <= levelX + levelWidth && 
          y >= startY && y <= startY + levelHeight) {
        
        // 检查关卡是否已解锁
        if (level.unlocked) {
          console.log(`选择关卡: ${level.id}`)
          this.selectLevel(i)
        } else {
          console.log('关卡未解锁')
          // 添加锁定关卡提示
          this.showLockedLevelTip(level, i)
        }
        
        break
      }
    }
    
    // 检查返回按钮
    const btnWidth = 100
    const btnHeight = 40
    const btnX = 20
    const btnY = 20
    
    if (x >= btnX && x <= btnX + btnWidth && 
        y >= btnY && y <= btnY + btnHeight) {
      console.log('返回按钮被点击')
      this.game.sceneManager.showScene('welcome')
    }
    
    // 检查返回主页按钮
    const homeBtnWidth = 100
    const homeBtnHeight = 40
    const homeBtnX = this.width - 120
    const homeBtnY = 20
    
    if (x >= homeBtnX && x <= homeBtnX + homeBtnWidth && 
        y >= homeBtnY && y <= homeBtnY + homeBtnHeight) {
      console.log('主页按钮被点击')
      this.game.sceneManager.showScene('welcome')
    }
  }
  
  selectLevel(index) {
    // 设置当前关卡
    this.dataManager.setCurrentLevel(index)
    
    // 开始游戏
    this.game.startGame()
  }
  
  render() {
    super.render()
    
    // 绘制背景图片
    if (this.bgImage) {
      // 计算缩放比例以覆盖整个画布
      const scale = Math.max(
        this.width / this.bgImage.width,
        this.height / this.bgImage.height
      )
      
      // 计算绘制尺寸
      const drawWidth = this.bgImage.width * scale
      const drawHeight = this.bgImage.height * scale
      
      // 计算居中位置
      const x = (this.width - drawWidth) / 2
      const y = (this.height - drawHeight) / 2
      
      // 绘制背景图片
      this.ctx.drawImage(
        this.bgImage,
        x, y,
        drawWidth, drawHeight
      )
    }
    
    // 绘制返回按钮
    this.drawButton(
      '返回',
      20,
      20,
      100,
      40,
      this.config.colors.secondary,
      '#ffffff',
      this.config.fonts.body
    )
    
    // 绘制关卡列表 - 水平布局
    const levelWidth = 200
    const levelHeight = 250
    const startX = 100
    const startY = this.height / 2 - levelHeight / 2
    
    // 更新关卡主题图标和颜色
    const worldThemes = [
      { 
        icon: '🌲', // 森林入口，用树木图标
        color: '#2E7D32', // 深绿色，代表森林
        name: '森林入口' 
      },
      { 
        icon: '🏛️', // 诡异遗迹，用遗迹/神庙图标
        color: '#5D4037', // 棕色，代表古老遗迹
        name: '诡异遗迹' 
      },
      { 
        icon: '👹', // 生物收集，用怪物图标
        color: '#C62828', // 红色，代表危险生物
        name: '生物收集' 
      },
      { 
        icon: '🔮', // 最终谜题，用水晶球图标
        color: '#4527A0', // 深紫色，代表神秘
        name: '最终谜题' 
      }
    ]
    
    for (let i = 0; i < this.levels.length; i++) {
      const level = this.levels[i]
      const levelX = startX + i * 250 - this.scrollX
      const theme = worldThemes[i % worldThemes.length]
      
      // 如果关卡在可视区域内才绘制
      if (levelX + levelWidth > 0 && levelX < this.width) {
        // 绘制关卡卡片阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.1)'
        this.ctx.shadowBlur = 10
        this.ctx.shadowOffsetY = 3
        
        // 绘制关卡卡片背景
        let bgColor = level.unlocked ? '#FFFFFF' : '#EEEEEE'
        this.drawRoundRect(levelX, startY, levelWidth, levelHeight, 15, bgColor)
        
        // 重置阴影
        this.ctx.shadowColor = 'transparent'
        
        // 绘制世界图标
        const iconSize = 50
        const iconX = levelX + levelWidth/2 - iconSize/2
        const iconY = startY + 30
        this.ctx.font = `${iconSize}px Arial`
        this.ctx.textAlign = 'center'
        this.ctx.textBaseline = 'middle'
        this.ctx.fillStyle = level.unlocked ? theme.color : '#CCCCCC'
        this.ctx.fillText(theme.icon, iconX + iconSize/2, iconY + iconSize/2)
        
        // 绘制关卡名称
        this.drawText(
          level.name,
          levelX + levelWidth/2,
          startY + 100,
          this.config.fonts.subtitle,
          level.unlocked ? theme.color : '#999999',
          'center'
        )
        
        // 绘制探索进度
        if (level.completed) {
          const stars = level.stars
          const totalStars = 3
          this.drawText(
            `探索完成 ${stars}/${totalStars} 🌟`,
            levelX + levelWidth/2,
            startY + 140,
            this.config.fonts.body,
            '#666666',
            'center'
          )
        } else if (level.unlocked) {
          this.drawText(
            '等待探索...',
            levelX + levelWidth/2,
            startY + 140,
            this.config.fonts.body,
            '#666666',
            'center'
          )
        } else {
          this.drawText(
            '关卡未开启',
            levelX + levelWidth/2,
            startY + 140,
            this.config.fonts.body,
            '#999999',
            'center'
          )
        }
        
        // 绘制探索要求
        this.drawText(
          `收集 ${level.passingScore} 颗星星`,
          levelX + levelWidth/2,
          startY + 170,
          this.config.fonts.small,
          level.unlocked ? '#666666' : '#999999',
          'center'
        )
        
        this.drawText(
          `开启下一关卡`,
          levelX + levelWidth/2,
          startY + 190,
          this.config.fonts.small,
          level.unlocked ? '#666666' : '#999999',
          'center'
        )
        
        // 如果关卡未解锁，绘制锁定图标
        if (!level.unlocked) {
          this.ctx.fillStyle = '#9E9E9E'
          this.ctx.font = '30px Arial'
          this.ctx.fillText('🔒', levelX + levelWidth/2, startY + 220)
        }
      }
    }
    
    // 渲染锁定关卡提示
    if (this.tipAnimation && this.tipAnimation.active && this.tipAnimation.alpha > 0) {
      // 绘制半透明背景
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.6)'
      this.ctx.globalAlpha = this.tipAnimation.alpha * 0.6
      this.ctx.fillRect(0, 0, this.width, this.height)
      this.ctx.globalAlpha = 1
      
      // 绘制提示框
      const boxWidth = this.width * 0.8
      const boxHeight = this.height * 0.5
      const boxX = this.width / 2 - boxWidth / 2
      const boxY = this.height * 0.4 - boxHeight / 2
      
      this.ctx.save()
      this.ctx.globalAlpha = this.tipAnimation.alpha
      this.ctx.translate(this.width / 2, boxY + boxHeight / 2)
      this.ctx.scale(this.tipAnimation.scale, this.tipAnimation.scale)
      this.ctx.translate(-this.width / 2, -(boxY + boxHeight / 2))
      
      // 绘制提示框背景
      this.drawRoundRect(boxX, boxY, boxWidth, boxHeight, 10, '#FFFFFF')
      
      // 绘制锁图标
      this.ctx.font = '30px Arial'
      this.ctx.fillStyle = '#FFD700'
      this.ctx.textAlign = 'center'
      this.ctx.fillText('🔒', this.width / 2, boxY + 40)
      
      // 绘制提示文本
      this.drawText(
        '关卡尚未开启',
        this.width / 2,
        boxY + 80,
        this.config.fonts.subtitle,
        '#FF5722',
        'center'
      )
      
      // 获取前一关信息
      const prevLevel = this.levels[this.tipAnimation.prevLevelIndex]
      
      if (prevLevel) {
        this.drawText(
          `需要先完成: ${prevLevel.name}`,
          this.width / 2,
          boxY + 120,
          this.config.fonts.body,
          '#2196F3',
          'center'
        )
        
        this.drawText(
          `(收集 ${prevLevel.passingScore} 颗星星)`,
          this.width / 2,
          boxY + 150,
          this.config.fonts.body,
          '#666666',
          'center'
        )
      }
      
      this.ctx.restore()
    }
  }
  
  // 获取难度星星显示
  getDifficultyStars(level) {
    const difficulty = Math.ceil(level.id / 2); // 简单的难度计算
    let stars = '';
    for (let i = 0; i < difficulty; i++) {
      stars += '⭐';
    }
    return stars;
  }
  
  // 绘制星级
  drawStars(x, y, count) {
    const starSize = 25;
    const starGap = 5;
    
    for (let i = 0; i < 3; i++) {
      const starX = x + i * (starSize + starGap);
      const color = i < count ? '#FFD700' : '#DDDDDD';
      
      // 绘制星星
      this.ctx.fillStyle = color;
      this.ctx.font = '25px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('★', starX, y);
    }
  }
  
  // 添加显示锁定关卡提示的方法
  showLockedLevelTip(level, index) {
    this.tipAnimation = {
      alpha: 0,
      scale: 0,
      active: true,
      level: level,
      prevLevelIndex: index - 1
    }
    
    // 淡入动画
    this.animationManager.addNumberChange(
      this.tipAnimation,
      'alpha',
      1,
      300,
      'easeOutQuad'
    )
    
    // 缩放动画
    this.animationManager.addScale(
      this.tipAnimation,
      0,
      1,
      300,
      'easeOutBack'
    )
    
    // 3秒后自动关闭提示
    setTimeout(() => {
      if (this.tipAnimation && this.tipAnimation.active) {
        this.animationManager.addNumberChange(
          this.tipAnimation,
          'alpha',
          0,
          300,
          'easeInQuad',
          () => {
            this.tipAnimation.active = false
          }
        )
      }
    }, 500)
  }
  
  // 在 update 方法中更新动画
  update() {
    if (this.animationManager) {
      this.animationManager.update()
    }
  }
  
  destroy() {
    // 清理事件
    wx.offTouchStart(this.touchStartHandler);
    wx.offTouchMove(this.touchMoveHandler);
    wx.offTouchEnd(this.touchEndHandler);
  }
} 